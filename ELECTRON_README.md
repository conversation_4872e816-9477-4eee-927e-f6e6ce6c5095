# GEDLI Invoice Generator - Electron Desktop App

This project has been converted to an Electron desktop application that can be built for Windows and Linux.

## Development

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Running in Development Mode

1. **Start the development environment:**
   ```bash
   npm run electron:dev
   ```
   This will start both the Vite dev server and Electron app simultaneously.

2. **Alternative development commands:**
   ```bash
   # Start Vite dev server only
   npm run dev
   
   # Start Electron only (requires Vite server to be running)
   npm run electron
   ```

## Building for Production

### Build for Current Platform
```bash
npm run electron:pack
```

### Build for Specific Platforms

**Windows:**
```bash
npm run electron:dist:win
```

**Linux:**
```bash
npm run electron:dist:linux
```

**Both Windows and Linux:**
```bash
npm run electron:dist:all
```

## Output Files

Built applications will be placed in the `dist-electron` directory:

### Windows
- `GEDLI Invoice Generator Setup.exe` - NSIS installer
- `GEDLI Invoice Generator.exe` - Portable executable

### Linux
- `GEDLI Invoice Generator.AppImage` - AppImage (universal Linux package)
- `gedli-invoice-generator_1.0.0_amd64.deb` - Debian package

## Features

- **Cross-platform:** Runs on Windows and Linux
- **Native menus:** Platform-appropriate application menus
- **Print support:** Native print functionality
- **Auto-updater ready:** Configured for future auto-update implementation
- **Secure:** Context isolation and disabled node integration for security

## Project Structure

```
├── electron/
│   ├── main.js          # Main Electron process
│   └── preload.js       # Preload script for secure communication
├── scripts/
│   └── electron-dev.js  # Development script
├── src/                 # React application source
├── dist/                # Built web application
├── dist-electron/       # Built Electron applications
└── package.json         # Project configuration with Electron scripts
```

## Configuration

The Electron configuration is defined in `package.json` under the `build` section:

- **App ID:** `com.gedli.invoice-generator`
- **Product Name:** `GEDLI Invoice Generator`
- **Windows targets:** NSIS installer and portable executable
- **Linux targets:** AppImage and Debian package

## Troubleshooting

### Development Issues

1. **Port conflicts:** The app uses port 5173 for Vite dev server
2. **Electron not starting:** Ensure Vite dev server is running first
3. **Build failures:** Check that all dependencies are installed

### Build Issues

1. **Missing dependencies:** Run `npm install` to ensure all packages are installed
2. **Platform-specific builds:** Some packages may require platform-specific dependencies
3. **Icon issues:** Ensure `public/favicon.ico` exists for app icons

## Security

The Electron app is configured with security best practices:
- Context isolation enabled
- Node integration disabled
- Preload script for secure IPC communication
- External link handling

## Future Enhancements

- Auto-updater implementation
- Code signing for distribution
- Mac OS support
- Custom app icons
- Splash screen
