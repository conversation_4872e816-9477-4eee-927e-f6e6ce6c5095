#!/bin/bash

echo "Testing the fixed GEDLI Invoice Generator AppImage..."

# Make sure the AppImage is executable
chmod +x "dist-electron/GEDLI Invoice Generator-1.0.0.AppImage"

echo "Starting AppImage..."
echo "This will run for 15 seconds to test if the white screen issue is fixed."
echo "Look for the application window to open with the dashboard (not a 404 page)."

# Run the AppImage in the background and capture output
"./dist-electron/GEDLI Invoice Generator-1.0.0.AppImage" > /tmp/appimage-output.log 2>&1 &
APP_PID=$!

echo "AppImage started with PID: $APP_PID"
echo "Waiting 15 seconds..."

# Wait for 15 seconds
sleep 15

# Check if the process is still running
if kill -0 $APP_PID 2>/dev/null; then
    echo "✅ Application is still running - this is good!"
    echo "Stopping application..."
    kill $APP_PID 2>/dev/null
    
    # Wait a bit for graceful shutdown
    sleep 2
    
    # Force kill if still running
    if kill -0 $APP_PID 2>/dev/null; then
        echo "Force killing application..."
        kill -9 $APP_PID 2>/dev/null
    fi
else
    echo "❌ Application exited early - check the logs"
fi

echo ""
echo "Application output:"
echo "=================="
cat /tmp/appimage-output.log
echo "=================="
echo ""
echo "Test completed."
echo "If you saw the application window open with the dashboard (not a 404 error), the fix worked!"
