import React from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Printer, ArrowLeft } from "lucide-react";

interface InvoiceData {
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: "amount" | "percentage";
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
}

interface InvoiceTemplateProps {
  invoiceData: InvoiceData;
  onBack: () => void;
}

const InvoiceTemplate = ({ invoiceData, onBack }: InvoiceTemplateProps) => {
  const invoiceNumber = `GDL-${Date.now().toString().slice(-6)}`;
  const currentDate = new Date().toLocaleDateString();

  const handlePrint = () => {
    window.print();
  };

  const calculateDiscountAmount = () => {
    if (invoiceData.discountType === "percentage") {
      return (invoiceData.subtotal * invoiceData.discount) / 100;
    }
    return invoiceData.discount;
  };

  const discountAmount = calculateDiscountAmount();

  return (
    <div className="w-full max-w-4xl">
      <div className="mb-4 flex gap-2 print:hidden">
        <Button
          onClick={onBack}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Form
        </Button>
        <Button
          onClick={handlePrint}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
        >
          <Printer className="h-4 w-4" />
          Print Invoice
        </Button>
      </div>

      <Card className="p-6 print:shadow-none print:border-none print:p-4 print:text-sm">
        <div className="flex justify-between items-start mb-6 print:mb-4">
          <div className="flex items-center gap-4">
            {/* Logo Placeholder */}
            <div className="w-20 h-20 bg-blue-100 border-2 border-blue-300 rounded-lg flex items-center justify-center print:w-16 print:h-16">
              <span className="text-blue-600 font-bold text-xs">LOGO</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-blue-600 mb-1 print:text-2xl print:mb-1">
                GEDLI
              </h1>
              <p className="text-gray-600 text-base print:text-sm">
                3D Printing Services
              </p>
              <p className="text-gray-500 text-sm print:text-xs">
                Professional 3D Printing Solutions
              </p>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold text-gray-800 mb-1 print:text-lg print:mb-1">
              INVOICE
            </h2>
            <p className="text-gray-600 text-sm print:text-xs">
              Invoice #: <span className="font-semibold">{invoiceNumber}</span>
            </p>
            <p className="text-gray-600 text-sm print:text-xs">
              Date: <span className="font-semibold">{currentDate}</span>
            </p>
          </div>
        </div>

        <Separator className="my-4 print:my-2" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print:gap-4 print:mb-4">
          <div>
            <h3 className="text-base font-semibold text-gray-800 mb-2 print:text-sm print:mb-1">
              Bill To
            </h3>
            <div className="space-y-1 print:space-y-0.5">
              <p className="text-sm font-semibold print:text-xs">
                {invoiceData.clientName}
              </p>
              <p className="text-sm text-gray-600 whitespace-pre-wrap print:text-xs">
                {invoiceData.clientAddress}
              </p>
            </div>
          </div>
          <div>
            <h3 className="text-base font-semibold text-gray-800 mb-2 print:text-sm print:mb-1">
              Company Information
            </h3>
            <div className="space-y-0.5 text-gray-600 text-sm print:text-xs print:space-y-0">
              <p>Gedli 3D Printing</p>
              <p>Professional 3D Printing</p>
              <p>Email: <EMAIL></p>
              <p>Phone: +216 55 652 468</p>
            </div>
          </div>
        </div>

        <div className="mb-6 print:mb-4">
          <h3 className="text-base font-semibold text-gray-800 mb-2 print:text-sm print:mb-1">
            Project Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 print:gap-2">
            <div>
              <span className="text-gray-600 text-sm print:text-xs">
                Project Name:
              </span>
              <p className="font-semibold text-sm print:text-xs">
                {invoiceData.projectName}
              </p>
            </div>
            <div>
              <span className="text-gray-600 text-sm print:text-xs">
                Date Started:
              </span>
              <p className="font-semibold text-sm print:text-xs">
                {new Date(invoiceData.dateStarted).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6 print:mb-4">
          <h3 className="text-base font-semibold text-gray-800 mb-3 print:text-sm print:mb-2">
            Cost Breakdown
          </h3>
          <div className="bg-gray-50 rounded-lg p-4 print:p-3">
            <div className="space-y-2 print:space-y-1">
              <div className="flex justify-between items-center text-sm print:text-xs">
                <span className="text-gray-700">Filament Cost</span>
                <span className="font-semibold">
                  {invoiceData.filamentCost.toFixed(2)} DT
                </span>
              </div>
              <div className="flex justify-between items-center text-sm print:text-xs">
                <span className="text-gray-700">Other Expenses</span>
                <span className="font-semibold">
                  {invoiceData.otherExpenses.toFixed(2)} DT
                </span>
              </div>
              <div className="flex justify-between items-center text-sm print:text-xs">
                <span className="text-gray-700">Shipping Cost</span>
                <span className="font-semibold">
                  {invoiceData.shippingCost.toFixed(2)} DT
                </span>
              </div>
              <Separator />
              <div className="flex justify-between items-center text-base print:text-sm">
                <span className="font-semibold text-gray-800">Subtotal</span>
                <span className="font-bold">
                  {invoiceData.subtotal.toFixed(2)} DT
                </span>
              </div>
              {invoiceData.discount > 0 && (
                <div className="flex justify-between items-center text-green-600 text-sm print:text-xs">
                  <span>
                    Discount Applied (
                    {invoiceData.discountType === "percentage"
                      ? `${invoiceData.discount}%`
                      : "Fixed Amount"}
                    )
                  </span>
                  <span className="font-semibold">
                    -{discountAmount.toFixed(2)} DT
                  </span>
                </div>
              )}
              <div className="flex justify-between items-center text-lg bg-blue-50 p-2 rounded-lg print:text-base print:p-1">
                <span className="font-bold text-blue-800">Total Amount</span>
                <span className="font-bold text-blue-800">
                  {invoiceData.total.toFixed(2)} DT
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6 print:mb-4">
          <h3 className="text-base font-semibold text-gray-800 mb-3 print:text-sm print:mb-2">
            Payment Information
          </h3>
          <div className="bg-gray-50 rounded-lg p-4 print:p-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 print:gap-2">
              <div>
                <span className="text-gray-600 block mb-1 text-sm print:text-xs">
                  Client Prepayment
                </span>
                <span className="font-semibold text-base print:text-sm">
                  {invoiceData.clientPrepayment.toFixed(2)} DT
                </span>
              </div>
              <div>
                <span className="text-gray-600 block mb-1 text-sm print:text-xs">
                  Remaining Balance
                </span>
                <span
                  className={`font-semibold text-base print:text-sm ${
                    invoiceData.remainingBalance > 0
                      ? "text-red-600"
                      : "text-green-600"
                  }`}
                >
                  {invoiceData.remainingBalance.toFixed(2)} DT
                </span>
              </div>
            </div>
          </div>
        </div>

        {invoiceData.notes && (
          <div className="mb-6 print:mb-4">
            <h3 className="text-base font-semibold text-gray-800 mb-2 print:text-sm print:mb-1">
              Project Notes
            </h3>
            <div className="bg-gray-50 rounded-lg p-3 print:p-2">
              <p className="text-gray-700 whitespace-pre-wrap text-sm print:text-xs">
                {invoiceData.notes}
              </p>
            </div>
          </div>
        )}

        <Separator className="my-4 print:my-2" />
        <div className="text-center text-gray-600 print:text-xs">
          <p className="mb-1">
            Thank you for choosing Gedli 3D Printing Services!
          </p>
          <p className="text-sm print:text-xs">
            For questions about this invoice, please contact us at
            <EMAIL>
          </p>
        </div>
      </Card>
    </div>
  );
};

export default InvoiceTemplate;
