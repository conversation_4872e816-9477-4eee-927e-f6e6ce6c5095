import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, Download, Eye, Database } from "lucide-react";
import { exportToExcel, exportToCSV } from "@/utils/exportUtils";
import { formatDateToDDMMYYYY } from "@/utils/dateUtils";

interface InvoiceData {
  id: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: "amount" | "percentage";
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
  createdAt: string;
}

interface InvoiceManagementProps {
  onViewInvoice: (invoice: InvoiceData) => void;
}

const InvoiceManagement = ({ onViewInvoice }: InvoiceManagementProps) => {
  const [invoices, setInvoices] = useState<InvoiceData[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredInvoices, setFilteredInvoices] = useState<InvoiceData[]>([]);

  useEffect(() => {
    const savedInvoices = localStorage.getItem("gedli-invoices");
    if (savedInvoices) {
      setInvoices(JSON.parse(savedInvoices));
    }
  }, []);

  useEffect(() => {
    const filtered = invoices.filter(
      (invoice) =>
        invoice.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredInvoices(filtered);
  }, [invoices, searchTerm]);

  const handleExportExcel = () => {
    exportToExcel(filteredInvoices, "gedli-invoices");
  };

  const handleExportCSV = () => {
    exportToCSV(filteredInvoices, "gedli-invoices");
  };

  return (
    <Card className="w-full max-w-6xl">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Database className="h-6 w-6" />
          Invoice Management
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search invoices by project name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleExportExcel}
              variant="outline"
              className="flex items-center gap-2"
              disabled={filteredInvoices.length === 0}
            >
              <Download className="h-4 w-4" />
              Export Excel
            </Button>
            <Button
              onClick={handleExportCSV}
              variant="outline"
              className="flex items-center gap-2"
              disabled={filteredInvoices.length === 0}
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </div>
        </div>

        {filteredInvoices.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {invoices.length === 0
              ? "No invoices found. Create your first invoice to get started."
              : "No invoices match your search criteria."}
          </div>
        ) : (
          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice ID</TableHead>
                  <TableHead>Project Name</TableHead>
                  <TableHead>Date Created</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell className="font-mono text-sm">
                      {invoice.id}
                    </TableCell>
                    <TableCell className="font-semibold">
                      {invoice.projectName}
                    </TableCell>
                    <TableCell>
                      {formatDateToDDMMYYYY(invoice.createdAt)}
                    </TableCell>
                    <TableCell className="font-semibold">
                      {invoice.total.toFixed(2)} DT
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onViewInvoice(invoice)}
                        className="flex items-center gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default InvoiceManagement;
