import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calculator, FileText, Plus, Users, FolderOpen } from "lucide-react";
import { Project, ClientInfo } from "@/types";
import { formatDisplayToDateInput } from "@/utils/dateUtils";
import { getProjects, getClients } from "@/utils/projectStorage";

interface InvoiceData {
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: "amount" | "percentage";
  notes: string;
}

interface InvoiceFormProps {
  onGenerateInvoice: (
    data: InvoiceData & {
      subtotal: number;
      total: number;
      remainingBalance: number;
    }
  ) => void;
  selectedProject?: Project | null;
}

const InvoiceForm = ({
  onGenerateInvoice,
  selectedProject,
}: InvoiceFormProps) => {
  const [mode, setMode] = useState<"existing" | "new">("existing");
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [clients, setClients] = useState<ClientInfo[]>([]);

  const [formData, setFormData] = useState<InvoiceData>({
    clientName: "",
    clientAddress: "",
    projectName: "",
    dateStarted: "",
    filamentCost: 0,
    otherExpenses: 0,
    shippingCost: 0,
    clientPrepayment: 0,
    discount: 0,
    discountType: "amount",
    notes: "",
  });

  // Load projects and clients on component mount
  useEffect(() => {
    setProjects(getProjects());
    setClients(getClients());
  }, []);

  // Populate form when a project is selected from props (from project management)
  useEffect(() => {
    if (selectedProject) {
      setMode("existing");
      setSelectedProjectId(selectedProject.id);
      populateFormFromProject(selectedProject);
    }
  }, [selectedProject]);

  // Handle project selection change
  useEffect(() => {
    if (mode === "existing" && selectedProjectId) {
      const project = projects.find((p) => p.id === selectedProjectId);
      if (project) {
        populateFormFromProject(project);
      }
    }
  }, [selectedProjectId, projects, mode]);

  // Handle client selection change (for new project mode)
  useEffect(() => {
    if (mode === "new" && selectedClientId) {
      const client = clients.find((c) => c.id === selectedClientId);
      if (client) {
        setFormData((prev) => ({
          ...prev,
          clientName: client.name,
          clientAddress: client.address,
        }));
      }
    }
  }, [selectedClientId, clients, mode]);

  const populateFormFromProject = (project: Project) => {
    setFormData({
      clientName: project.clientInfo.name,
      clientAddress: project.clientInfo.address,
      projectName: project.name,
      dateStarted: project.dateStarted
        ? formatDisplayToDateInput(project.dateStarted)
        : "",
      filamentCost: 0,
      otherExpenses: 0,
      shippingCost: 0,
      clientPrepayment: 0,
      discount: 0,
      discountType: "amount",
      notes: project.notes || "",
    });
  };

  const calculateTotals = (data: InvoiceData) => {
    const subtotal = data.filamentCost + data.otherExpenses + data.shippingCost;
    let discountAmount = 0;

    if (data.discountType === "percentage") {
      discountAmount = (subtotal * data.discount) / 100;
    } else {
      discountAmount = data.discount;
    }

    const total = subtotal - discountAmount;
    const remainingBalance = total - data.clientPrepayment;
    return { subtotal, total, remainingBalance };
  };

  const handleInputChange = (
    field: keyof InvoiceData,
    value: string | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleModeChange = (newMode: "existing" | "new") => {
    setMode(newMode);
    setSelectedProjectId("");
    setSelectedClientId("");
    // Reset form data when switching modes
    setFormData({
      clientName: "",
      clientAddress: "",
      projectName: "",
      dateStarted: "",
      filamentCost: 0,
      otherExpenses: 0,
      shippingCost: 0,
      clientPrepayment: 0,
      discount: 0,
      discountType: "amount",
      notes: "",
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const calculations = calculateTotals(formData);
    onGenerateInvoice({ ...formData, ...calculations });
  };

  const { subtotal, total, remainingBalance } = calculateTotals(formData);

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2 text-2xl">
          <FileText className="h-6 w-6" />
          Invoice Generator - Gedli
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Mode Selection */}
          <Tabs
            value={mode}
            onValueChange={handleModeChange}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="existing" className="flex items-center gap-2">
                <FolderOpen className="h-4 w-4" />
                From Existing Project
              </TabsTrigger>
              <TabsTrigger value="new" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Project/Client
              </TabsTrigger>
            </TabsList>

            <TabsContent value="existing" className="space-y-4 mt-6">
              {projects.length > 0 ? (
                <div className="space-y-2">
                  <Label htmlFor="projectSelect">Select Project</Label>
                  <Select
                    value={selectedProjectId}
                    onValueChange={setSelectedProjectId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an existing project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{project.name}</span>
                            <span className="text-sm text-gray-500">
                              {project.clientInfo.name} • {project.status}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">No projects found</p>
                  <p className="text-sm text-gray-500">
                    Create a project first or use the "New Project/Client" tab
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="new" className="space-y-4 mt-6">
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Label htmlFor="clientSelect">
                      Select Existing Client (Optional)
                    </Label>
                    <Select
                      value={selectedClientId}
                      onValueChange={setSelectedClientId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a client or enter new details below" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">
                          <span className="text-gray-500">
                            Enter new client details
                          </span>
                        </SelectItem>
                        {clients.map((client) => (
                          <SelectItem key={client.id} value={client.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{client.name}</span>
                              {client.company && (
                                <span className="text-sm text-gray-500">
                                  {client.company}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientName">Client Name</Label>
                    <Input
                      id="clientName"
                      value={formData.clientName}
                      onChange={(e) =>
                        handleInputChange("clientName", e.target.value)
                      }
                      placeholder="Enter client name"
                      required
                      disabled={!!selectedClientId}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="projectName">Project Name</Label>
                    <Input
                      id="projectName"
                      value={formData.projectName}
                      onChange={(e) =>
                        handleInputChange("projectName", e.target.value)
                      }
                      placeholder="Enter project name"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientAddress">Client Address</Label>
                  <Textarea
                    id="clientAddress"
                    value={formData.clientAddress}
                    onChange={(e) =>
                      handleInputChange("clientAddress", e.target.value)
                    }
                    placeholder="Enter client address"
                    rows={2}
                    required
                    disabled={!!selectedClientId}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Show form fields only when project is selected or in new mode */}
          {(mode === "existing" && selectedProjectId) || mode === "new" ? (
            <>
              {mode === "existing" && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <FolderOpen className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      Selected Project Details
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">Client:</span>
                      <p className="font-medium">{formData.clientName}</p>
                    </div>
                    <div>
                      <span className="text-blue-700">Project:</span>
                      <p className="font-medium">{formData.projectName}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="dateStarted">Date Started</Label>
                <Input
                  id="dateStarted"
                  type="date"
                  value={formData.dateStarted}
                  onChange={(e) =>
                    handleInputChange("dateStarted", e.target.value)
                  }
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="filamentCost">Filament Cost (DT)</Label>
                  <Input
                    id="filamentCost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.filamentCost || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "filamentCost",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="otherExpenses">Other Expenses (DT)</Label>
                  <Input
                    id="otherExpenses"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.otherExpenses || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "otherExpenses",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shippingCost">Shipping Cost (DT)</Label>
                  <Input
                    id="shippingCost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.shippingCost || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "shippingCost",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="discountType">Discount Type</Label>
                  <Select
                    value={formData.discountType}
                    onValueChange={(value: "amount" | "percentage") =>
                      handleInputChange("discountType", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="amount">Fixed Amount (DT)</SelectItem>
                      <SelectItem value="percentage">Percentage (%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="discount">
                    Discount{" "}
                    {formData.discountType === "percentage" ? "(%)" : "(DT)"}
                  </Label>
                  <Input
                    id="discount"
                    type="number"
                    step={
                      formData.discountType === "percentage" ? "0.1" : "0.01"
                    }
                    min="0"
                    max={
                      formData.discountType === "percentage" ? "100" : undefined
                    }
                    value={formData.discount || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "discount",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder={
                      formData.discountType === "percentage" ? "0.0" : "0.00"
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clientPrepayment">
                    Client Prepayment (DT)
                  </Label>
                  <Input
                    id="clientPrepayment"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.clientPrepayment || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "clientPrepayment",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Additional notes or project details"
                  rows={3}
                />
              </div>

              <div className="bg-gray-50 p-4 rounded-lg border">
                <div className="flex items-center gap-2 mb-3">
                  <Calculator className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg">Calculation Summary</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Subtotal:</span>
                    <p className="font-semibold">{subtotal.toFixed(2)} DT</p>
                  </div>
                  <div>
                    <span className="text-gray-600">
                      Total (after discount):
                    </span>
                    <p className="font-semibold">{total.toFixed(2)} DT</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Remaining Balance:</span>
                    <p
                      className={`font-semibold ${
                        remainingBalance > 0 ? "text-red-600" : "text-green-600"
                      }`}
                    >
                      {remainingBalance.toFixed(2)} DT
                    </p>
                  </div>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3"
                disabled={mode === "existing" && !selectedProjectId}
              >
                Generate Invoice
              </Button>
            </>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">
                Select a project or enter new details to continue
              </p>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default InvoiceForm;
