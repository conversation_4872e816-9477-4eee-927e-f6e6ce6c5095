
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calculator, FileText } from 'lucide-react';

interface InvoiceData {
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: 'amount' | 'percentage';
  notes: string;
}

interface InvoiceFormProps {
  onGenerateInvoice: (data: InvoiceData & { subtotal: number; total: number; remainingBalance: number }) => void;
}

const InvoiceForm = ({ onGenerateInvoice }: InvoiceFormProps) => {
  const [formData, setFormData] = useState<InvoiceData>({
    clientName: '',
    clientAddress: '',
    projectName: '',
    dateStarted: '',
    filamentCost: 0,
    otherExpenses: 0,
    shippingCost: 0,
    clientPrepayment: 0,
    discount: 0,
    discountType: 'amount',
    notes: ''
  });

  const calculateTotals = (data: InvoiceData) => {
    const subtotal = data.filamentCost + data.otherExpenses + data.shippingCost;
    let discountAmount = 0;
    
    if (data.discountType === 'percentage') {
      discountAmount = (subtotal * data.discount) / 100;
    } else {
      discountAmount = data.discount;
    }
    
    const total = subtotal - discountAmount;
    const remainingBalance = total - data.clientPrepayment;
    return { subtotal, total, remainingBalance };
  };

  const handleInputChange = (field: keyof InvoiceData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const calculations = calculateTotals(formData);
    onGenerateInvoice({ ...formData, ...calculations });
  };

  const { subtotal, total, remainingBalance } = calculateTotals(formData);

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2 text-2xl">
          <FileText className="h-6 w-6" />
          Invoice Generator - Gedli
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="clientName">Client Name</Label>
              <Input
                id="clientName"
                value={formData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                placeholder="Enter client name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                value={formData.projectName}
                onChange={(e) => handleInputChange('projectName', e.target.value)}
                placeholder="Enter project name"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="clientAddress">Client Address</Label>
            <Textarea
              id="clientAddress"
              value={formData.clientAddress}
              onChange={(e) => handleInputChange('clientAddress', e.target.value)}
              placeholder="Enter client address"
              rows={2}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dateStarted">Date Started</Label>
            <Input
              id="dateStarted"
              type="date"
              value={formData.dateStarted}
              onChange={(e) => handleInputChange('dateStarted', e.target.value)}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="filamentCost">Filament Cost (DT)</Label>
              <Input
                id="filamentCost"
                type="number"
                step="0.01"
                min="0"
                value={formData.filamentCost || ''}
                onChange={(e) => handleInputChange('filamentCost', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="otherExpenses">Other Expenses (DT)</Label>
              <Input
                id="otherExpenses"
                type="number"
                step="0.01"
                min="0"
                value={formData.otherExpenses || ''}
                onChange={(e) => handleInputChange('otherExpenses', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shippingCost">Shipping Cost (DT)</Label>
              <Input
                id="shippingCost"
                type="number"
                step="0.01"
                min="0"
                value={formData.shippingCost || ''}
                onChange={(e) => handleInputChange('shippingCost', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discountType">Discount Type</Label>
              <Select value={formData.discountType} onValueChange={(value: 'amount' | 'percentage') => handleInputChange('discountType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select discount type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="amount">Fixed Amount (DT)</SelectItem>
                  <SelectItem value="percentage">Percentage (%)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="discount">
                Discount {formData.discountType === 'percentage' ? '(%)' : '(DT)'}
              </Label>
              <Input
                id="discount"
                type="number"
                step={formData.discountType === 'percentage' ? "0.1" : "0.01"}
                min="0"
                max={formData.discountType === 'percentage' ? "100" : undefined}
                value={formData.discount || ''}
                onChange={(e) => handleInputChange('discount', parseFloat(e.target.value) || 0)}
                placeholder={formData.discountType === 'percentage' ? '0.0' : '0.00'}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="clientPrepayment">Client Prepayment (DT)</Label>
              <Input
                id="clientPrepayment"
                type="number"
                step="0.01"
                min="0"
                value={formData.clientPrepayment || ''}
                onChange={(e) => handleInputChange('clientPrepayment', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes or project details"
              rows={3}
            />
          </div>

          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-3">
              <Calculator className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold text-lg">Calculation Summary</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Subtotal:</span>
                <p className="font-semibold">{subtotal.toFixed(2)} DT</p>
              </div>
              <div>
                <span className="text-gray-600">Total (after discount):</span>
                <p className="font-semibold">{total.toFixed(2)} DT</p>
              </div>
              <div>
                <span className="text-gray-600">Remaining Balance:</span>
                <p className={`font-semibold ${remainingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {remainingBalance.toFixed(2)} DT
                </p>
              </div>
            </div>
          </div>

          <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-3">
            Generate Invoice
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default InvoiceForm;
