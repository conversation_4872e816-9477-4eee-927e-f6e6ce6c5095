import React from "react";
import { Button } from "@/components/ui/button";
import { FileText, Database, FolderOpen, BarChart3, Users } from "lucide-react";

interface NavigationProps {
  activeTab:
    | "dashboard"
    | "projects"
    | "clients"
    | "invoices"
    | "create"
    | "manage";
  onTabChange: (
    tab: "dashboard" | "projects" | "clients" | "invoices" | "create" | "manage"
  ) => void;
}

const Navigation = ({ activeTab, onTabChange }: NavigationProps) => {
  return (
    <div className="flex flex-wrap gap-2 mb-6">
      <Button
        variant={activeTab === "dashboard" ? "default" : "outline"}
        onClick={() => onTabChange("dashboard")}
        className="flex items-center gap-2"
      >
        <BarChart3 className="h-4 w-4" />
        Dashboard
      </Button>
      <Button
        variant={activeTab === "projects" ? "default" : "outline"}
        onClick={() => onTabChange("projects")}
        className="flex items-center gap-2"
      >
        <FolderOpen className="h-4 w-4" />
        Projects
      </Button>
      <Button
        variant={activeTab === "clients" ? "default" : "outline"}
        onClick={() => onTabChange("clients")}
        className="flex items-center gap-2"
      >
        <Users className="h-4 w-4" />
        Clients
      </Button>
      <Button
        variant={activeTab === "invoices" ? "default" : "outline"}
        onClick={() => onTabChange("invoices")}
        className="flex items-center gap-2"
      >
        <Database className="h-4 w-4" />
        Invoices
      </Button>
      <Button
        variant={activeTab === "create" ? "default" : "outline"}
        onClick={() => onTabChange("create")}
        className="flex items-center gap-2"
      >
        <FileText className="h-4 w-4" />
        Create Invoice
      </Button>
    </div>
  );
};

export default Navigation;
