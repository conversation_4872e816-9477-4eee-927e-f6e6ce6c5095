
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Database, FolderOpen, BarChart3, Users } from 'lucide-react';

interface NavigationProps {
  activeTab: 'dashboard' | 'projects' | 'clients' | 'invoices' | 'create' | 'manage';
  onTabChange: (tab: 'dashboard' | 'projects' | 'clients' | 'invoices' | 'create' | 'manage') => void;
}

const Navigation = ({ activeTab, onTabChange }: NavigationProps) => {
  return (
    <div className="flex gap-2 mb-6">
      <Button
        variant={activeTab === 'create' ? 'default' : 'outline'}
        onClick={() => onTabChange('create')}
        className="flex items-center gap-2"
      >
        <FileText className="h-4 w-4" />
        Create Invoice
      </Button>
      <Button
        variant={activeTab === 'manage' ? 'default' : 'outline'}
        onClick={() => onTabChange('manage')}
        className="flex items-center gap-2"
      >
        <Database className="h-4 w-4" />
        Manage Invoices
      </Button>
    </div>
  );
};

export default Navigation;
