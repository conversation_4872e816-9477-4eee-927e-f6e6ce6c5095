import React, { useState } from "react";
import InvoiceForm from "@/components/InvoiceForm";
import InvoiceTemplate from "@/components/InvoiceTemplate";
import InvoiceManagement from "@/components/InvoiceManagement";
import ProjectManagement from "@/components/ProjectManagement";
import ClientManagement from "@/components/ClientManagement";
import Dashboard from "@/components/Dashboard";
import Navigation from "@/components/Navigation";
import { saveInvoice } from "@/utils/invoiceStorage";
import { Project, InvoiceData as IInvoiceData } from "@/types";

interface InvoiceData {
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: "amount" | "percentage";
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
}

interface StoredInvoiceData extends InvoiceData {
  id: string;
  createdAt: string;
}

const Index = () => {
  const [activeTab, setActiveTab] = useState<
    "dashboard" | "projects" | "clients" | "invoices" | "create" | "manage"
  >("dashboard");
  const [invoiceData, setInvoiceData] = useState<
    InvoiceData | StoredInvoiceData | null
  >(null);
  const [showInvoice, setShowInvoice] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  const handleGenerateInvoice = (data: InvoiceData) => {
    const savedInvoice = saveInvoice(data);
    setInvoiceData(savedInvoice);
    setShowInvoice(true);
    console.log("Invoice generated and saved:", savedInvoice);
  };

  const handleViewInvoice = (invoice: StoredInvoiceData) => {
    setInvoiceData(invoice);
    setShowInvoice(true);
  };

  const handleBackToForm = () => {
    setShowInvoice(false);
    setInvoiceData(null);
  };

  const handleBackToManagement = () => {
    setShowInvoice(false);
    setActiveTab("invoices");
  };

  const handleCreateInvoiceFromProject = (project: Project) => {
    setSelectedProject(project);
    setActiveTab("create");
  };

  const handleNavigate = (tab: string, id?: string) => {
    setActiveTab(tab as any);
    if (id) {
      // Handle navigation to specific items if needed
      console.log("Navigate to", tab, "with ID", id);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col items-center justify-center">
          {!showInvoice ? (
            <div className="w-full flex flex-col items-center">
              <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

              {activeTab === "dashboard" && (
                <Dashboard onNavigate={handleNavigate} />
              )}
              {activeTab === "projects" && (
                <ProjectManagement
                  onCreateInvoice={handleCreateInvoiceFromProject}
                />
              )}
              {activeTab === "clients" && <ClientManagement />}
              {activeTab === "invoices" && (
                <InvoiceManagement onViewInvoice={handleViewInvoice} />
              )}
              {activeTab === "create" && (
                <InvoiceForm
                  onGenerateInvoice={handleGenerateInvoice}
                  selectedProject={selectedProject}
                />
              )}
            </div>
          ) : (
            invoiceData && (
              <InvoiceTemplate
                invoiceData={invoiceData}
                onBack={
                  activeTab === "create"
                    ? handleBackToForm
                    : handleBackToManagement
                }
              />
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
