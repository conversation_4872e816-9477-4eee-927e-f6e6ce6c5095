
import React, { useState } from 'react';
import InvoiceForm from '@/components/InvoiceForm';
import InvoiceTemplate from '@/components/InvoiceTemplate';
import InvoiceManagement from '@/components/InvoiceManagement';
import Navigation from '@/components/Navigation';
import { saveInvoice } from '@/utils/invoiceStorage';

interface InvoiceData {
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: 'amount' | 'percentage';
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
}

interface StoredInvoiceData extends InvoiceData {
  id: string;
  createdAt: string;
}

const Index = () => {
  const [activeTab, setActiveTab] = useState<'create' | 'manage'>('create');
  const [invoiceData, setInvoiceData] = useState<InvoiceData | StoredInvoiceData | null>(null);
  const [showInvoice, setShowInvoice] = useState(false);

  const handleGenerateInvoice = (data: InvoiceData) => {
    const savedInvoice = saveInvoice(data);
    setInvoiceData(savedInvoice);
    setShowInvoice(true);
    console.log('Invoice generated and saved:', savedInvoice);
  };

  const handleViewInvoice = (invoice: StoredInvoiceData) => {
    setInvoiceData(invoice);
    setShowInvoice(true);
  };

  const handleBackToForm = () => {
    setShowInvoice(false);
    setInvoiceData(null);
  };

  const handleBackToManagement = () => {
    setShowInvoice(false);
    setActiveTab('manage');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col items-center justify-center">
          {!showInvoice ? (
            <div className="w-full flex flex-col items-center">
              <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
              
              {activeTab === 'create' ? (
                <InvoiceForm onGenerateInvoice={handleGenerateInvoice} />
              ) : (
                <InvoiceManagement onViewInvoice={handleViewInvoice} />
              )}
            </div>
          ) : (
            invoiceData && (
              <InvoiceTemplate 
                invoiceData={invoiceData} 
                onBack={activeTab === 'create' ? handleBackToForm : handleBackToManagement}
              />
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
