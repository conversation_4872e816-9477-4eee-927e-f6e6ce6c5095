// Shared types for the application

export interface ClientInfo {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  clientId: string;
  clientInfo: ClientInfo; // Denormalized for easier access
  status: 'draft' | 'in-progress' | 'completed' | 'invoiced' | 'cancelled';
  dateCreated: string;
  dateStarted?: string;
  dateCompleted?: string;
  estimatedCost?: number;
  actualCost?: number;
  notes: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceData {
  id: string;
  projectId?: string; // Link to project if created from project
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: 'amount' | 'percentage';
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
  createdAt: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
}

export interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalRevenue: number;
  pendingRevenue: number;
  totalClients: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'project_created' | 'project_completed' | 'invoice_generated' | 'payment_received' | 'client_added';
  title: string;
  description: string;
  timestamp: string;
  relatedId?: string; // ID of related project, invoice, or client
}

export interface RevenueData {
  month: string;
  revenue: number;
  invoices: number;
}

export interface ProjectStatusCount {
  status: Project['status'];
  count: number;
  color: string;
}
