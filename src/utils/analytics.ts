import { Project, InvoiceData, DashboardStats, RevenueData, ProjectStatusCount } from '@/types';
import { getProjects, getClients, getActivities } from './projectStorage';
import { getInvoices } from './invoiceStorage';

export const getDashboardStats = (): DashboardStats => {
  const projects = getProjects();
  const invoices = getInvoices();
  const clients = getClients();
  const activities = getActivities();

  const totalProjects = projects.length;
  const activeProjects = projects.filter(p => p.status === 'in-progress').length;
  const completedProjects = projects.filter(p => p.status === 'completed' || p.status === 'invoiced').length;

  const totalRevenue = invoices
    .filter(inv => inv.status === 'paid')
    .reduce((sum, inv) => sum + inv.total, 0);

  const pendingRevenue = invoices
    .filter(inv => inv.status === 'sent' || inv.status === 'overdue')
    .reduce((sum, inv) => sum + inv.total, 0);

  const totalClients = clients.length;
  const recentActivity = activities.slice(0, 10);

  return {
    totalProjects,
    activeProjects,
    completedProjects,
    totalRevenue,
    pendingRevenue,
    totalClients,
    recentActivity
  };
};

export const getRevenueByMonth = (): RevenueData[] => {
  const invoices = getInvoices();
  const monthlyData: { [key: string]: { revenue: number; count: number } } = {};

  invoices
    .filter(inv => inv.status === 'paid')
    .forEach(invoice => {
      const date = new Date(invoice.createdAt);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { revenue: 0, count: 0 };
      }
      
      monthlyData[monthKey].revenue += invoice.total;
      monthlyData[monthKey].count += 1;
    });

  return Object.entries(monthlyData)
    .map(([month, data]) => ({
      month,
      revenue: data.revenue,
      invoices: data.count
    }))
    .sort((a, b) => a.month.localeCompare(b.month))
    .slice(-12); // Last 12 months
};

export const getProjectStatusCounts = (): ProjectStatusCount[] => {
  const projects = getProjects();
  const statusColors = {
    draft: '#94a3b8',
    'in-progress': '#3b82f6',
    completed: '#10b981',
    invoiced: '#8b5cf6',
    cancelled: '#ef4444'
  };

  const statusCounts = projects.reduce((acc, project) => {
    acc[project.status] = (acc[project.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(statusCounts).map(([status, count]) => ({
    status: status as Project['status'],
    count,
    color: statusColors[status as keyof typeof statusColors]
  }));
};

export const getTopClients = (): Array<{ clientName: string; revenue: number; projectCount: number }> => {
  const projects = getProjects();
  const invoices = getInvoices();
  
  const clientStats: { [clientName: string]: { revenue: number; projectCount: number } } = {};

  // Count projects per client
  projects.forEach(project => {
    const clientName = project.clientInfo.name;
    if (!clientStats[clientName]) {
      clientStats[clientName] = { revenue: 0, projectCount: 0 };
    }
    clientStats[clientName].projectCount += 1;
  });

  // Calculate revenue per client
  invoices
    .filter(inv => inv.status === 'paid')
    .forEach(invoice => {
      const clientName = invoice.clientName;
      if (!clientStats[clientName]) {
        clientStats[clientName] = { revenue: 0, projectCount: 0 };
      }
      clientStats[clientName].revenue += invoice.total;
    });

  return Object.entries(clientStats)
    .map(([clientName, stats]) => ({
      clientName,
      revenue: stats.revenue,
      projectCount: stats.projectCount
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10); // Top 10 clients
};

export const getRecentProjects = (limit: number = 5): Project[] => {
  const projects = getProjects();
  return projects
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, limit);
};

export const getProjectsNeedingAttention = (): Project[] => {
  const projects = getProjects();
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return projects.filter(project => {
    // Projects in progress for more than 30 days
    if (project.status === 'in-progress' && project.dateStarted) {
      const startDate = new Date(project.dateStarted);
      return startDate < thirtyDaysAgo;
    }
    
    // Completed projects not yet invoiced
    if (project.status === 'completed') {
      return true;
    }
    
    return false;
  });
};
