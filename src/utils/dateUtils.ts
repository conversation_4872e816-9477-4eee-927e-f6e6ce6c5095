/**
 * Utility functions for date formatting
 */

/**
 * Formats a date to dd/mm/yyyy format
 * @param date - Date object, date string, or timestamp
 * @returns Formatted date string in dd/mm/yyyy format
 */
export const formatDateToDDMMYYYY = (date: Date | string | number): string => {
  const dateObj = new Date(date);
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();
  
  return `${day}/${month}/${year}`;
};

/**
 * Formats current date to dd/mm/yyyy format
 * @returns Current date in dd/mm/yyyy format
 */
export const getCurrentDateFormatted = (): string => {
  return formatDateToDDMMYYYY(new Date());
};

/**
 * Converts a date input value (yyyy-mm-dd) to dd/mm/yyyy format for display
 * @param dateInputValue - Date input value in yyyy-mm-dd format
 * @returns Formatted date string in dd/mm/yyyy format
 */
export const formatDateInputToDisplay = (dateInputValue: string): string => {
  if (!dateInputValue) return '';
  return formatDateToDDMMYYYY(dateInputValue);
};

/**
 * Converts a dd/mm/yyyy date string to yyyy-mm-dd format for date inputs
 * @param ddmmyyyy - Date string in dd/mm/yyyy format
 * @returns Date string in yyyy-mm-dd format
 */
export const formatDisplayToDateInput = (ddmmyyyy: string): string => {
  if (!ddmmyyyy) return '';
  
  const parts = ddmmyyyy.split('/');
  if (parts.length !== 3) return '';
  
  const [day, month, year] = parts;
  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
};
