
interface InvoiceData {
  id: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: 'amount' | 'percentage';
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
  createdAt: string;
}

export const exportToCSV = (invoices: InvoiceData[], filename: string) => {
  const headers = [
    'Invoice ID',
    'Project Name',
    'Date Started',
    'Date Created',
    'Filament Cost (DT)',
    'Other Expenses (DT)',
    'Shipping Cost (DT)',
    'Subtotal (DT)',
    'Discount (DT)',
    'Discount Type',
    'Total (DT)',
    'Client Prepayment (DT)',
    'Remaining Balance (DT)',
    'Notes'
  ];

  const csvContent = [
    headers.join(','),
    ...invoices.map(invoice => [
      invoice.id,
      `"${invoice.projectName}"`,
      invoice.dateStarted,
      new Date(invoice.createdAt).toLocaleDateString(),
      invoice.filamentCost.toFixed(2),
      invoice.otherExpenses.toFixed(2),
      invoice.shippingCost.toFixed(2),
      invoice.subtotal.toFixed(2),
      invoice.discount.toFixed(2),
      invoice.discountType,
      invoice.total.toFixed(2),
      invoice.clientPrepayment.toFixed(2),
      invoice.remainingBalance.toFixed(2),
      `"${invoice.notes.replace(/"/g, '""')}"`
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const exportToExcel = (invoices: InvoiceData[], filename: string) => {
  const headers = [
    'Invoice ID',
    'Project Name',
    'Date Started',
    'Date Created',
    'Filament Cost (DT)',
    'Other Expenses (DT)',
    'Shipping Cost (DT)',
    'Subtotal (DT)',
    'Discount (DT)',
    'Discount Type',
    'Total (DT)',
    'Client Prepayment (DT)',
    'Remaining Balance (DT)',
    'Notes'
  ];

  const data = invoices.map(invoice => [
    invoice.id,
    invoice.projectName,
    invoice.dateStarted,
    new Date(invoice.createdAt).toLocaleDateString(),
    invoice.filamentCost,
    invoice.otherExpenses,
    invoice.shippingCost,
    invoice.subtotal,
    invoice.discount,
    invoice.discountType,
    invoice.total,
    invoice.clientPrepayment,
    invoice.remainingBalance,
    invoice.notes
  ]);

  const worksheet = [headers, ...data];
  
  let csvContent = worksheet.map(row => 
    row.map(cell => 
      typeof cell === 'string' && cell.includes(',') ? `"${cell}"` : cell
    ).join(',')
  ).join('\n');

  const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.xlsx`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
