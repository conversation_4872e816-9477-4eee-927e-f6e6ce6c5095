
interface InvoiceData {
  id: string;
  clientName: string;
  clientAddress: string;
  projectName: string;
  dateStarted: string;
  filamentCost: number;
  otherExpenses: number;
  shippingCost: number;
  clientPrepayment: number;
  discount: number;
  discountType: 'amount' | 'percentage';
  remainingBalance: number;
  notes: string;
  subtotal: number;
  total: number;
  createdAt: string;
}

export const saveInvoice = (invoiceData: Omit<InvoiceData, 'id' | 'createdAt'>) => {
  const invoice: InvoiceData = {
    ...invoiceData,
    id: `GDL-${Date.now().toString().slice(-6)}`,
    createdAt: new Date().toISOString()
  };

  const existingInvoices = getInvoices();
  const updatedInvoices = [...existingInvoices, invoice];
  
  localStorage.setItem('gedli-invoices', JSON.stringify(updatedInvoices));
  return invoice;
};

export const getInvoices = (): InvoiceData[] => {
  const saved = localStorage.getItem('gedli-invoices');
  return saved ? JSON.parse(saved) : [];
};

export const getInvoiceById = (id: string): InvoiceData | null => {
  const invoices = getInvoices();
  return invoices.find(invoice => invoice.id === id) || null;
};
