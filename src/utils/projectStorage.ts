import { Project, ClientInfo, ActivityItem } from '@/types';

const PROJECTS_KEY = 'gedli-projects';
const CLIENTS_KEY = 'gedli-clients';
const ACTIVITIES_KEY = 'gedli-activities';

// Project Management
export const saveProject = (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Project => {
  const project: Project = {
    ...projectData,
    id: `PRJ-${Date.now().toString().slice(-6)}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const existingProjects = getProjects();
  const updatedProjects = [...existingProjects, project];
  
  localStorage.setItem(PROJECTS_KEY, JSON.stringify(updatedProjects));
  
  // Add activity
  addActivity({
    type: 'project_created',
    title: 'New Project Created',
    description: `Project "${project.name}" was created for ${project.clientInfo.name}`,
    relatedId: project.id
  });
  
  return project;
};

export const updateProject = (id: string, updates: Partial<Project>): Project | null => {
  const projects = getProjects();
  const projectIndex = projects.findIndex(p => p.id === id);
  
  if (projectIndex === -1) return null;
  
  const updatedProject = {
    ...projects[projectIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  projects[projectIndex] = updatedProject;
  localStorage.setItem(PROJECTS_KEY, JSON.stringify(projects));
  
  return updatedProject;
};

export const getProjects = (): Project[] => {
  const saved = localStorage.getItem(PROJECTS_KEY);
  return saved ? JSON.parse(saved) : [];
};

export const getProjectById = (id: string): Project | null => {
  const projects = getProjects();
  return projects.find(project => project.id === id) || null;
};

export const deleteProject = (id: string): boolean => {
  const projects = getProjects();
  const filteredProjects = projects.filter(project => project.id !== id);
  
  if (filteredProjects.length === projects.length) return false;
  
  localStorage.setItem(PROJECTS_KEY, JSON.stringify(filteredProjects));
  return true;
};

// Client Management
export const saveClient = (clientData: Omit<ClientInfo, 'id' | 'createdAt' | 'updatedAt'>): ClientInfo => {
  const client: ClientInfo = {
    ...clientData,
    id: `CLI-${Date.now().toString().slice(-6)}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  const existingClients = getClients();
  const updatedClients = [...existingClients, client];
  
  localStorage.setItem(CLIENTS_KEY, JSON.stringify(updatedClients));
  
  // Add activity
  addActivity({
    type: 'client_added',
    title: 'New Client Added',
    description: `Client "${client.name}" was added to the system`,
    relatedId: client.id
  });
  
  return client;
};

export const updateClient = (id: string, updates: Partial<ClientInfo>): ClientInfo | null => {
  const clients = getClients();
  const clientIndex = clients.findIndex(c => c.id === id);
  
  if (clientIndex === -1) return null;
  
  const updatedClient = {
    ...clients[clientIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  clients[clientIndex] = updatedClient;
  localStorage.setItem(CLIENTS_KEY, JSON.stringify(clients));
  
  return updatedClient;
};

export const getClients = (): ClientInfo[] => {
  const saved = localStorage.getItem(CLIENTS_KEY);
  return saved ? JSON.parse(saved) : [];
};

export const getClientById = (id: string): ClientInfo | null => {
  const clients = getClients();
  return clients.find(client => client.id === id) || null;
};

// Activity Management
export const addActivity = (activityData: Omit<ActivityItem, 'id' | 'timestamp'>): ActivityItem => {
  const activity: ActivityItem = {
    ...activityData,
    id: `ACT-${Date.now().toString().slice(-6)}`,
    timestamp: new Date().toISOString()
  };

  const existingActivities = getActivities();
  const updatedActivities = [activity, ...existingActivities].slice(0, 100); // Keep only last 100 activities
  
  localStorage.setItem(ACTIVITIES_KEY, JSON.stringify(updatedActivities));
  
  return activity;
};

export const getActivities = (): ActivityItem[] => {
  const saved = localStorage.getItem(ACTIVITIES_KEY);
  return saved ? JSON.parse(saved) : [];
};
