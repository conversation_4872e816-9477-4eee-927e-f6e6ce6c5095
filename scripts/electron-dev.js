import { spawn } from "child_process";
import { createServer } from "vite";
import electron from "electron";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let electronProcess = null;
let manualRestart = false;

async function startVite() {
  const server = await createServer({
    configFile: path.resolve(__dirname, "../vite.config.ts"),
    mode: "development",
  });

  await server.listen(5173);
  console.log("Vite dev server started on http://localhost:5173");
  return server;
}

function startElectron() {
  if (electronProcess) {
    manualRestart = true;
    electronProcess.kill();
    electronProcess = null;
  }

  electronProcess = spawn(
    electron,
    [path.resolve(__dirname, "../electron.js")],
    {
      env: {
        ...process.env,
        NODE_ENV: "development",
      },
      stdio: "inherit",
    }
  );

  electronProcess.on("close", (code) => {
    if (!manualRestart) {
      process.exit(code);
    }
    manualRestart = false;
  });

  electronProcess.on("error", (error) => {
    console.error("Failed to start Electron:", error);
  });
}

async function main() {
  try {
    // Start Vite dev server
    const viteServer = await startVite();

    // Wait a bit for Vite to be ready
    setTimeout(() => {
      startElectron();
    }, 2000);

    // Handle process termination
    process.on("SIGINT", () => {
      if (electronProcess) {
        electronProcess.kill();
      }
      viteServer.close();
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      if (electronProcess) {
        electronProcess.kill();
      }
      viteServer.close();
      process.exit(0);
    });
  } catch (error) {
    console.error("Failed to start development environment:", error);
    process.exit(1);
  }
}

main();
