const fs = require('fs');
const path = require('path');

// Build the web app first
console.log('Building web application...');
const { execSync } = require('child_process');

try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('Web build completed successfully.');
} catch (error) {
  console.error('Web build failed:', error.message);
  process.exit(1);
}

// Clean up the HTML file for Electron
console.log('Cleaning up HTML for Electron...');

const htmlPath = path.join(__dirname, '../dist/index.html');
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Remove the external script that causes issues in Electron
htmlContent = htmlContent.replace(
  /<script src="https:\/\/cdn\.gpteng\.co\/gptengineer\.js" type="module"><\/script>/g,
  ''
);

// Remove the comment that references the script
htmlContent = htmlContent.replace(
  /<!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->/g,
  ''
);

// Ensure all asset paths are relative
htmlContent = htmlContent.replace(/src="\.\/assets\//g, 'src="./assets/');
htmlContent = htmlContent.replace(/href="\.\/assets\//g, 'href="./assets/');

// Write the cleaned HTML back
fs.writeFileSync(htmlPath, htmlContent);
console.log('HTML cleanup completed.');

// Now run electron-builder
console.log('Building Electron application...');
try {
  execSync('electron-builder', { stdio: 'inherit' });
  console.log('Electron build completed successfully.');
} catch (error) {
  console.error('Electron build failed:', error.message);
  process.exit(1);
}
