const { spawn } = require('child_process');
const path = require('path');

console.log('Testing built Electron application...');

// Test the unpacked version first
const electronPath = path.join(__dirname, '../dist-electron/linux-unpacked/gedli-invoice-generator');

console.log('Starting Electron app from:', electronPath);
console.log('This will run for 10 seconds to check for errors...');

const electronProcess = spawn(electronPath, [], {
  stdio: ['ignore', 'pipe', 'pipe'],
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
});

let hasOutput = false;

electronProcess.stdout.on('data', (data) => {
  hasOutput = true;
  console.log('STDOUT:', data.toString());
});

electronProcess.stderr.on('data', (data) => {
  hasOutput = true;
  console.log('STDERR:', data.toString());
});

electronProcess.on('error', (error) => {
  console.error('Failed to start Electron app:', error);
});

electronProcess.on('exit', (code, signal) => {
  console.log(`Electron app exited with code ${code}, signal ${signal}`);
  if (code === 0) {
    console.log('✅ Application started successfully!');
  } else {
    console.log('❌ Application failed to start properly.');
  }
});

// Kill the process after 10 seconds
setTimeout(() => {
  console.log('Stopping test...');
  electronProcess.kill('SIGTERM');
  
  setTimeout(() => {
    if (!electronProcess.killed) {
      console.log('Force killing...');
      electronProcess.kill('SIGKILL');
    }
  }, 2000);
}, 10000);

// If no output after 5 seconds, it might be running fine
setTimeout(() => {
  if (!hasOutput) {
    console.log('✅ No errors detected - application appears to be running normally');
  }
}, 5000);
