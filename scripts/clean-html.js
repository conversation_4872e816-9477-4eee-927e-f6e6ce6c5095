const fs = require("fs");
const path = require("path");

console.log("Cleaning up HTML for Electron...");

const htmlPath = path.join(__dirname, "../dist/index.html");

if (!fs.existsSync(htmlPath)) {
  console.error("HTML file not found at:", htmlPath);
  process.exit(1);
}

let htmlContent = fs.readFileSync(htmlPath, "utf8");

// Remove the external script that causes issues in Electron
htmlContent = htmlContent.replace(
  /<script src="https:\/\/cdn\.gpteng\.co\/gptengineer\.js" type="module"><\/script>/g,
  ""
);

// Remove the comment that references the script
htmlContent = htmlContent.replace(
  /<!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->/g,
  ""
);

// Ensure all asset paths are relative
htmlContent = htmlContent.replace(/src="\.\/assets\//g, 'src="./assets/');
htmlContent = htmlContent.replace(/href="\.\/assets\//g, 'href="./assets/');

// Remove any existing CSP that might be too restrictive for Electron
htmlContent = htmlContent.replace(
  /<meta http-equiv="Content-Security-Policy"[^>]*>/g,
  ""
);

// Also remove CSP with different quote styles
htmlContent = htmlContent.replace(
  /<meta http-equiv='Content-Security-Policy'[^>]*>/g,
  ""
);

console.log("Removed CSP headers for Electron compatibility");

// Write the cleaned HTML back
fs.writeFileSync(htmlPath, htmlContent);
console.log("HTML cleanup completed successfully.");
