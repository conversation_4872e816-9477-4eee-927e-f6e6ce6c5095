const { spawn } = require('child_process');
const path = require('path');

console.log('Testing Electron build...');

// Run the built Electron app
const electronPath = path.join(__dirname, '../dist-electron/linux-unpacked/gedli-invoice-generator');

console.log('Starting Electron app from:', electronPath);

const electronProcess = spawn(electronPath, [], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
});

electronProcess.on('error', (error) => {
  console.error('Failed to start Electron app:', error);
});

electronProcess.on('exit', (code) => {
  console.log(`Electron app exited with code ${code}`);
});

// Kill the process after 30 seconds for testing
setTimeout(() => {
  console.log('Stopping test...');
  electronProcess.kill();
}, 30000);
