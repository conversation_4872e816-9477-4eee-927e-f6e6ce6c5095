#!/bin/bash

echo "Testing GEDLI Invoice Generator AppImage..."

# Make sure the AppImage is executable
chmod +x "dist-electron/GEDLI Invoice Generator-1.0.0.AppImage"

echo "Starting AppImage (will run for 10 seconds)..."
echo "Check if the application window opens without white screen."

# Run the AppImage in the background
"./dist-electron/GEDLI Invoice Generator-1.0.0.AppImage" &
APP_PID=$!

echo "AppImage started with PID: $APP_PID"
echo "Waiting 10 seconds..."

# Wait for 10 seconds
sleep 10

# Kill the application
echo "Stopping application..."
kill $APP_PID 2>/dev/null

# Wait a bit for graceful shutdown
sleep 2

# Force kill if still running
if kill -0 $APP_PID 2>/dev/null; then
    echo "Force killing application..."
    kill -9 $APP_PID 2>/dev/null
fi

echo "Test completed. Check if the application opened successfully without white screen."
