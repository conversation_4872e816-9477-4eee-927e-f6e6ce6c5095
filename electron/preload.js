const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Menu events
  onMenuNewInvoice: (callback) => ipcRenderer.on('menu-new-invoice', callback),
  onMenuAbout: (callback) => ipcRenderer.on('menu-about', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // Platform info
  platform: process.platform,
  
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Print functionality
  print: () => ipcRenderer.invoke('print-window'),
  
  // File operations (for future use)
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  openFile: () => ipcRenderer.invoke('open-file'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // Theme
  getTheme: () => ipcRenderer.invoke('get-theme'),
  setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
  onThemeChanged: (callback) => ipcRenderer.on('theme-changed', callback)
});

// DOM Content Loaded event
window.addEventListener('DOMContentLoaded', () => {
  // Add platform class to body for platform-specific styling
  document.body.classList.add(`platform-${process.platform}`);
  
  // Add electron class to indicate we're running in Electron
  document.body.classList.add('electron-app');
  
  // Set app version in title if available
  const updateTitle = () => {
    const title = document.title;
    if (!title.includes('GEDLI')) {
      document.title = 'GEDLI Invoice Generator';
    }
  };
  
  updateTitle();
  
  // Watch for title changes
  const observer = new MutationObserver(updateTitle);
  observer.observe(document.querySelector('title') || document.head, {
    childList: true,
    subtree: true
  });
});
